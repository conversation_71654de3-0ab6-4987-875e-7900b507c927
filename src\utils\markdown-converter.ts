/**
 * Markdown和HTML双向转换工具
 * 保持语法一致性，避免 **粗体** 和 __粗体__ 混淆
 */

// 方案1: 使用unified生态系统 (推荐)
export class UnifiedMarkdownConverter {
  private remarkToHtml: any;
  private htmlToRemark: any;

  constructor() {
    // 这里需要安装相关依赖后才能使用
    // npm install unified remark-parse remark-rehype rehype-stringify rehype-parse rehype-remark remark-stringify
  }

  async initializeUnified() {
    const { unified } = await import('unified');
    const remarkParse = await import('remark-parse');
    const remarkRehype = await import('remark-rehype');
    const rehypeStringify = await import('rehype-stringify');
    const rehypeParse = await import('rehype-parse');
    const rehypeRemark = await import('rehype-remark');
    const remarkStringify = await import('remark-stringify');

    // Markdown → HTML
    this.remarkToHtml = unified()
      .use(remarkParse.default)
      .use(remarkRehype.default)
      .use(rehypeStringify.default);

    // HTML → Markdown (保持原始语法风格)
    this.htmlToRemark = unified()
      .use(rehypeParse.default)
      .use(rehypeRemark.default)
      .use(remarkStringify.default, {
        // 配置选项保持语法一致性
        emphasis: '*',      // 使用 *斜体* 而不是 _斜体_
        strong: '**',       // 使用 **粗体** 而不是 __粗体__
        bullet: '-',        // 使用 - 作为列表符号
        fence: '```',       // 使用 ``` 作为代码块
        fences: true,       // 优先使用围栏代码块
        listItemIndent: 'one' // 列表缩进方式
      });
  }

  async markdownToHtml(markdown: string): Promise<string> {
    if (!this.remarkToHtml) await this.initializeUnified();
    const result = await this.remarkToHtml.process(markdown);
    return String(result);
  }

  async htmlToMarkdown(html: string): Promise<string> {
    if (!this.htmlToRemark) await this.initializeUnified();
    const result = await this.htmlToRemark.process(html);
    return String(result);
  }
}

// 方案2: 使用turndown + marked (更简单的方案)
export class TurndownMarkdownConverter {
  private marked: any;
  private turndownService: any;

  constructor() {
    // 需要安装: npm install marked turndown
  }

  async initializeTurndown() {
    const { marked } = await import('marked');
    const TurndownService = await import('turndown');

    this.marked = marked;
    
    // 配置turndown保持语法一致性
    this.turndownService = new TurndownService.default({
      headingStyle: 'atx',        // 使用 # 标题风格
      hr: '---',                  // 使用 --- 作为分隔线
      bulletListMarker: '-',      // 使用 - 作为列表标记
      codeBlockStyle: 'fenced',   // 使用围栏代码块
      fence: '```',               // 代码块围栏符号
      emDelimiter: '*',           // 斜体使用 *
      strongDelimiter: '**',      // 粗体使用 **
      linkStyle: 'inlined',       // 内联链接风格
      linkReferenceStyle: 'full'  // 引用链接风格
    });

    // 自定义规则确保语法一致性
    this.turndownService.addRule('preserveEmphasis', {
      filter: ['em', 'i'],
      replacement: (content: string) => `*${content}*`
    });

    this.turndownService.addRule('preserveStrong', {
      filter: ['strong', 'b'],
      replacement: (content: string) => `**${content}**`
    });
  }

  async markdownToHtml(markdown: string): Promise<string> {
    if (!this.marked) await this.initializeTurndown();
    return this.marked.parse(markdown);
  }

  async htmlToMarkdown(html: string): Promise<string> {
    if (!this.turndownService) await this.initializeTurndown();
    return this.turndownService.turndown(html);
  }
}

// 方案3: 自定义AST保持完全一致性
export class CustomMarkdownConverter {
  private originalSyntaxMap = new Map<string, string>();

  /**
   * 解析markdown时保存原始语法信息
   */
  private preserveOriginalSyntax(markdown: string): string {
    // 保存原始的粗体语法
    const strongMatches = markdown.match(/(\*\*|__)(.*?)\1/g);
    if (strongMatches) {
      strongMatches.forEach((match, index) => {
        const key = `__STRONG_${index}__`;
        this.originalSyntaxMap.set(key, match);
      });
    }

    // 保存原始的斜体语法
    const emMatches = markdown.match(/(\*|_)(.*?)\1/g);
    if (emMatches) {
      emMatches.forEach((match, index) => {
        const key = `__EM_${index}__`;
        this.originalSyntaxMap.set(key, match);
      });
    }

    return markdown;
  }

  /**
   * 恢复原始语法
   */
  private restoreOriginalSyntax(markdown: string): string {
    let result = markdown;
    this.originalSyntaxMap.forEach((originalSyntax, key) => {
      result = result.replace(key, originalSyntax);
    });
    return result;
  }

  async markdownToHtml(markdown: string): Promise<string> {
    // 这里需要实现自定义的markdown解析器
    // 或者使用现有库但保存语法信息
    this.preserveOriginalSyntax(markdown);
    
    // 使用任意markdown库转换
    const { marked } = await import('marked');
    return marked.parse(markdown);
  }

  async htmlToMarkdown(html: string): Promise<string> {
    // 转换回markdown
    const TurndownService = await import('turndown');
    const turndownService = new TurndownService.default();
    let result = turndownService.turndown(html);
    
    // 恢复原始语法
    result = this.restoreOriginalSyntax(result);
    
    return result;
  }
}

// 使用示例和测试函数
export async function testMarkdownConsistency() {
  const testMarkdown = `
# 标题测试

这是一个测试文档，包含不同的语法：

**粗体文本1** 和 __粗体文本2__

*斜体文本1* 和 _斜体文本2_

- 列表项1
- 列表项2

\`\`\`javascript
console.log('代码块');
\`\`\`

[链接](https://example.com)
`;

  console.log('原始Markdown:');
  console.log(testMarkdown);

  // 测试unified方案
  const unifiedConverter = new UnifiedMarkdownConverter();
  const html1 = await unifiedConverter.markdownToHtml(testMarkdown);
  const markdown1 = await unifiedConverter.htmlToMarkdown(html1);
  
  console.log('\n=== Unified方案 ===');
  console.log('HTML:', html1);
  console.log('转换回的Markdown:', markdown1);
  console.log('语法是否一致:', testMarkdown.trim() === markdown1.trim());

  // 测试turndown方案
  const turndownConverter = new TurndownMarkdownConverter();
  const html2 = await turndownConverter.markdownToHtml(testMarkdown);
  const markdown2 = await turndownConverter.htmlToMarkdown(html2);
  
  console.log('\n=== Turndown方案 ===');
  console.log('HTML:', html2);
  console.log('转换回的Markdown:', markdown2);
  console.log('语法是否一致:', testMarkdown.trim() === markdown2.trim());
}

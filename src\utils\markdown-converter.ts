/**
 * Markdown和HTML双向转换工具
 * 保持语法一致性，避免 **粗体** 和 __粗体__ 混淆
 */

import { marked } from 'marked';
import TurndownService from 'turndown';

// 使用turndown + marked的转换器
export class TurndownMarkdownConverter {
  private turndownService: TurndownService;
  private initialized = false;

  constructor() {
    // 初始化turndown服务
    this.turndownService = new TurndownService({
      headingStyle: 'atx',        // 使用 # 标题风格
      hr: '---',                  // 使用 --- 作为分隔线
      bulletListMarker: '-',      // 使用 - 作为列表标记
      codeBlockStyle: 'fenced',   // 使用围栏代码块
      fence: '```',               // 代码块围栏符号
      emDelimiter: '*',           // 斜体使用 *
      strongDelimiter: '**',      // 粗体使用 **
      linkStyle: 'inlined',       // 内联链接风格
      linkReferenceStyle: 'full'  // 引用链接风格
    });

    this.initializeRules();
  }

  private initializeRules() {
    // 自定义规则确保语法一致性
    this.turndownService.addRule('preserveEmphasis', {
      filter: ['em', 'i'],
      replacement: (content: string) => `*${content}*`
    });

    this.turndownService.addRule('preserveStrong', {
      filter: ['strong', 'b'],
      replacement: (content: string) => `**${content}**`
    });

    // 确保代码块使用围栏风格
    this.turndownService.addRule('preserveCodeBlock', {
      filter: 'pre',
      replacement: (content: string, node: any) => {
        const codeElement = node.querySelector('code');
        if (codeElement) {
          const language = codeElement.className.replace('language-', '') || '';
          return `\n\`\`\`${language}\n${codeElement.textContent}\n\`\`\`\n`;
        }
        return `\n\`\`\`\n${content}\n\`\`\`\n`;
      }
    });

    this.initialized = true;
  }

  async markdownToHtml(markdown: string): Promise<string> {
    return marked.parse(markdown);
  }

  async htmlToMarkdown(html: string): Promise<string> {
    if (!this.initialized) {
      this.initializeRules();
    }
    return this.turndownService.turndown(html);
  }
}

// 自定义转换器（保持原始语法）
export class CustomMarkdownConverter {
  private originalSyntaxMap = new Map<string, string>();
  private turndownService: TurndownService;

  constructor() {
    this.turndownService = new TurndownService({
      strongDelimiter: '**',
      emDelimiter: '*',
      bulletListMarker: '-',
      codeBlockStyle: 'fenced',
      fence: '```'
    });
  }

  /**
   * 解析markdown时保存原始语法信息
   */
  private preserveOriginalSyntax(markdown: string): string {
    // 保存原始的粗体语法
    const strongMatches = markdown.match(/(\*\*|__)(.*?)\1/g);
    if (strongMatches) {
      strongMatches.forEach((match, index) => {
        const key = `__STRONG_${index}__`;
        this.originalSyntaxMap.set(key, match);
      });
    }

    // 保存原始的斜体语法
    const emMatches = markdown.match(/(\*|_)(.*?)\1/g);
    if (emMatches) {
      emMatches.forEach((match, index) => {
        const key = `__EM_${index}__`;
        this.originalSyntaxMap.set(key, match);
      });
    }

    return markdown;
  }

  /**
   * 恢复原始语法
   */
  private restoreOriginalSyntax(markdown: string): string {
    let result = markdown;
    this.originalSyntaxMap.forEach((originalSyntax, key) => {
      result = result.replace(key, originalSyntax);
    });
    return result;
  }

  async markdownToHtml(markdown: string): Promise<string> {
    this.preserveOriginalSyntax(markdown);
    return marked.parse(markdown);
  }

  async htmlToMarkdown(html: string): Promise<string> {
    let result = this.turndownService.turndown(html);
    result = this.restoreOriginalSyntax(result);
    return result;
  }
}

// 使用示例和测试函数
export async function testMarkdownConsistency() {
  const testMarkdown = `# 标题测试

这是一个测试文档，包含不同的语法：

**粗体文本1** 和 __粗体文本2__

*斜体文本1* 和 _斜体文本2_

- 列表项1
- 列表项2

\`\`\`javascript
console.log('代码块');
\`\`\`

[链接](https://example.com)`;

  console.log('原始Markdown:');
  console.log(testMarkdown);

  // 测试turndown方案
  const turndownConverter = new TurndownMarkdownConverter();
  const html2 = await turndownConverter.markdownToHtml(testMarkdown);
  const markdown2 = await turndownConverter.htmlToMarkdown(html2);

  console.log('\n=== Turndown方案 ===');
  console.log('HTML:', html2);
  console.log('转换回的Markdown:', markdown2);
  console.log('语法是否一致:', testMarkdown.trim() === markdown2.trim());

  // 测试自定义方案
  const customConverter = new CustomMarkdownConverter();
  const html3 = await customConverter.markdownToHtml(testMarkdown);
  const markdown3 = await customConverter.htmlToMarkdown(html3);

  console.log('\n=== 自定义方案 ===');
  console.log('HTML:', html3);
  console.log('转换回的Markdown:', markdown3);
  console.log('语法是否一致:', testMarkdown.trim() === markdown3.trim());
}

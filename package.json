{"name": "markdwon_html", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"marked": "^16.1.2", "turndown": "^7.2.0", "vue": "^3.5.18"}, "devDependencies": {"@types/marked": "^6.0.0", "@types/turndown": "^5.0.5", "@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.1.0", "vue-tsc": "^3.0.5"}}
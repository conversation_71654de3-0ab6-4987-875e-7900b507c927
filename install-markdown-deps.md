# Markdown双向转换库安装指南

## 方案1: unified生态系统 (最推荐)

```bash
npm install unified remark-parse remark-rehype rehype-stringify rehype-parse rehype-remark remark-stringify
```

**特点：**
- ✅ 基于AST，转换最精确
- ✅ 高度可配置，可完全控制语法风格
- ✅ 插件生态丰富
- ✅ 可以实现完全可逆转换
- ❌ 学习曲线较陡峭
- ❌ 包体积较大

## 方案2: turndown + marked (推荐)

```bash
npm install marked turndown
npm install @types/marked @types/turndown --save-dev
```

**特点：**
- ✅ 使用简单，API直观
- ✅ turndown配置灵活，可保持语法一致性
- ✅ 包体积适中
- ✅ 社区活跃，文档完善
- ❌ 某些复杂语法可能有细微差异

## 方案3: markdown-it + turndown

```bash
npm install markdown-it turndown
npm install @types/markdown-it @types/turndown --save-dev
```

**特点：**
- ✅ markdown-it功能强大，插件丰富
- ✅ 性能优秀
- ❌ 配置复杂度中等

## 推荐使用方案

根据你的需求，我推荐使用 **方案2 (turndown + marked)**，因为：

1. **简单易用**：API直观，上手快
2. **配置灵活**：turndown支持详细的语法风格配置
3. **社区支持**：两个库都有活跃的社区和完善的文档
4. **性能良好**：转换速度快，包体积合理

## 语法一致性配置关键点

### TurndownService配置选项

```javascript
const turndownService = new TurndownService({
  headingStyle: 'atx',        // # 标题 vs 下划线标题
  hr: '---',                  // 分隔线样式
  bulletListMarker: '-',      // 列表标记: - vs *
  codeBlockStyle: 'fenced',   // 代码块: ``` vs 缩进
  fence: '```',               // 围栏符号
  emDelimiter: '*',           // 斜体: * vs _
  strongDelimiter: '**',      // 粗体: ** vs __
  linkStyle: 'inlined',       // 链接样式
  linkReferenceStyle: 'full'  // 引用链接样式
});
```

### 自定义规则示例

```javascript
// 确保粗体始终使用 **
turndownService.addRule('preserveStrong', {
  filter: ['strong', 'b'],
  replacement: (content) => `**${content}**`
});

// 确保斜体始终使用 *
turndownService.addRule('preserveEmphasis', {
  filter: ['em', 'i'],
  replacement: (content) => `*${content}*`
});
```

## 测试用例

建议使用以下测试用例验证转换的一致性：

```markdown
# 标题测试

**粗体1** __粗体2__ *斜体1* _斜体2_

- 列表项1
* 列表项2

```javascript
console.log('代码');
```

[链接](https://example.com)

> 引用文本

| 表格 | 测试 |
|------|------|
| 单元格1 | 单元格2 |
```

理想情况下，markdown → html → markdown 后应该保持语法风格一致。

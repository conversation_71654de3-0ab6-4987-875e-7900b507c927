<template>
  <div class="markdown-converter">
    <h1>Markdown ↔ HTML 双向转换测试</h1>
    
    <div class="converter-controls">
      <label>
        选择转换器:
        <select v-model="selectedConverter">
          <option value="turndown">Turndown + Marked (推荐)</option>
          <option value="custom">自定义转换器</option>
        </select>
      </label>
    </div>

    <div class="conversion-area">
      <div class="input-section">
        <h3>原始 Markdown</h3>
        <textarea 
          v-model="originalMarkdown" 
          placeholder="输入Markdown文本..."
          rows="10"
        ></textarea>
        <button @click="convertToHtml" :disabled="loading">
          {{ loading ? '转换中...' : 'Markdown → HTML' }}
        </button>
      </div>

      <div class="output-section">
        <h3>HTML 输出</h3>
        <div class="html-preview" v-html="htmlOutput"></div>
        <textarea 
          v-model="htmlOutput" 
          readonly 
          rows="10"
          placeholder="HTML输出将显示在这里..."
        ></textarea>
        <button @click="convertToMarkdown" :disabled="loading || !htmlOutput">
          {{ loading ? '转换中...' : 'HTML → Markdown' }}
        </button>
      </div>

      <div class="result-section">
        <h3>转换回的 Markdown</h3>
        <textarea 
          v-model="convertedMarkdown" 
          readonly 
          rows="10"
          placeholder="转换回的Markdown将显示在这里..."
        ></textarea>
        
        <div class="consistency-check">
          <h4>一致性检查</h4>
          <div class="check-result" :class="consistencyClass">
            {{ consistencyMessage }}
          </div>
          
          <details v-if="!isConsistent">
            <summary>查看差异</summary>
            <div class="diff-view">
              <div class="diff-original">
                <h5>原始:</h5>
                <pre>{{ originalMarkdown }}</pre>
              </div>
              <div class="diff-converted">
                <h5>转换后:</h5>
                <pre>{{ convertedMarkdown }}</pre>
              </div>
            </div>
          </details>
        </div>
      </div>
    </div>

    <div class="test-cases">
      <h3>测试用例</h3>
      <div class="test-buttons">
        <button @click="loadTestCase('basic')">基础语法</button>
        <button @click="loadTestCase('emphasis')">强调语法</button>
        <button @click="loadTestCase('lists')">列表</button>
        <button @click="loadTestCase('code')">代码块</button>
        <button @click="loadTestCase('complex')">复杂混合</button>
      </div>
    </div>

    <div class="error-message" v-if="errorMessage">
      <strong>错误:</strong> {{ errorMessage }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  TurndownMarkdownConverter,
  CustomMarkdownConverter
} from '../utils/markdown-converter'

const selectedConverter = ref('turndown')
const originalMarkdown = ref('')
const htmlOutput = ref('')
const convertedMarkdown = ref('')
const loading = ref(false)
const errorMessage = ref('')

// 转换器实例
const converters = {
  turndown: new TurndownMarkdownConverter(),
  custom: new CustomMarkdownConverter()
}

// 一致性检查
const isConsistent = computed(() => {
  if (!originalMarkdown.value || !convertedMarkdown.value) return true
  return originalMarkdown.value.trim() === convertedMarkdown.value.trim()
})

const consistencyClass = computed(() => ({
  'consistent': isConsistent.value,
  'inconsistent': !isConsistent.value
}))

const consistencyMessage = computed(() => {
  if (!originalMarkdown.value || !convertedMarkdown.value) {
    return '请先进行转换测试'
  }
  return isConsistent.value ? '✅ 语法完全一致' : '❌ 语法存在差异'
})

// 转换函数
async function convertToHtml() {
  if (!originalMarkdown.value.trim()) return
  
  loading.value = true
  errorMessage.value = ''
  
  try {
    const converter = converters[selectedConverter.value as keyof typeof converters]
    htmlOutput.value = await converter.markdownToHtml(originalMarkdown.value)
  } catch (error) {
    errorMessage.value = `转换失败: ${error instanceof Error ? error.message : String(error)}`
  } finally {
    loading.value = false
  }
}

async function convertToMarkdown() {
  if (!htmlOutput.value.trim()) return
  
  loading.value = true
  errorMessage.value = ''
  
  try {
    const converter = converters[selectedConverter.value as keyof typeof converters]
    convertedMarkdown.value = await converter.htmlToMarkdown(htmlOutput.value)
  } catch (error) {
    errorMessage.value = `转换失败: ${error instanceof Error ? error.message : String(error)}`
  } finally {
    loading.value = false
  }
}

// 测试用例
const testCases = {
  basic: `# 基础语法测试

这是一个段落。

## 二级标题

这是另一个段落。`,

  emphasis: `# 强调语法测试

**粗体文本1** 和 __粗体文本2__

*斜体文本1* 和 _斜体文本2_

***粗斜体*** 和 ___粗斜体___`,

  lists: `# 列表测试

无序列表:
- 项目1
- 项目2
  - 子项目1
  - 子项目2

有序列表:
1. 第一项
2. 第二项
   1. 子项目1
   2. 子项目2`,

  code: `# 代码测试

内联代码: \`console.log('hello')\`

代码块:
\`\`\`javascript
function hello() {
  console.log('Hello, World!');
}
\`\`\`

缩进代码:

    const x = 1;
    const y = 2;`,

  complex: `# 复杂混合测试

## 表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| **粗体** | *斜体* | \`代码\` |
| 数据1 | 数据2 | 数据3 |

## 引用

> 这是一个引用
> 
> > 嵌套引用
> 
> 引用中的 **粗体** 和 *斜体*

## 链接和图片

[链接文本](https://example.com)

![图片alt](https://via.placeholder.com/150)

---

水平分隔线上方和下方的内容。`
}

function loadTestCase(caseType: keyof typeof testCases) {
  originalMarkdown.value = testCases[caseType]
  htmlOutput.value = ''
  convertedMarkdown.value = ''
  errorMessage.value = ''
}

// 初始化时加载基础测试用例
loadTestCase('emphasis')
</script>

<style scoped>
.markdown-converter {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: system-ui, -apple-system, sans-serif;
}

.converter-controls {
  margin-bottom: 20px;
}

.converter-controls select {
  margin-left: 10px;
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.conversion-area {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.input-section, .output-section, .result-section {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
}

.input-section h3, .output-section h3, .result-section h3 {
  margin-top: 0;
  color: #333;
}

textarea {
  width: 100%;
  min-height: 200px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  resize: vertical;
}

button {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

button:hover:not(:disabled) {
  background: #0056b3;
}

button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.html-preview {
  border: 1px solid #eee;
  padding: 10px;
  margin: 10px 0;
  background: #f9f9f9;
  border-radius: 4px;
  min-height: 100px;
}

.consistency-check {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
}

.check-result {
  font-weight: bold;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 10px;
}

.check-result.consistent {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.check-result.inconsistent {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.diff-view {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-top: 10px;
}

.diff-original, .diff-converted {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
}

.diff-original h5, .diff-converted h5 {
  margin: 0 0 10px 0;
}

.diff-original pre, .diff-converted pre {
  margin: 0;
  white-space: pre-wrap;
  font-size: 12px;
}

.test-cases {
  margin-bottom: 20px;
}

.test-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.test-buttons button {
  background: #28a745;
  margin-top: 0;
}

.test-buttons button:hover {
  background: #1e7e34;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 10px;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .conversion-area {
    grid-template-columns: 1fr;
  }
  
  .diff-view {
    grid-template-columns: 1fr;
  }
}
</style>
